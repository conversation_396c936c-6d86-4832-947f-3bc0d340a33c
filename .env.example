# Discord Bot Configuration
DISCORD_TOKEN=OTk3NTk5Mzc3NzM0NTIwODMy.G0dwc1.bbPXkvlJ80A3JZNs9ZiVnhKMlU6DHi5fd_nSEM
CLIENT_ID=997599377734520832
GUILD_ID=1396705329991127151

# MongoDB Configuration
MONGODB_URI=mongodb+srv://congos:<EMAIL>/?retryWrites=true&w=majority&appName=cacique3bolas
# Para MongoDB Atlas: mongodb+srv://username:<EMAIL>/discord-store-bot

# Bot Configuration
BOT_PREFIX=!
NODE_ENV=development

# Logging
LOG_LEVEL=info
