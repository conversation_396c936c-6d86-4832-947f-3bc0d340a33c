import { logger } from '../utils/logger.js';

/**
 * Manipula comandos slash
 * @param {ChatInputCommandInteraction} interaction 
 */
export async function handleSlashCommand(interaction) {
    const command = interaction.client.commands.get(interaction.commandName);

    if (!command) {
        logger.warn(`Comando não encontrado: ${interaction.commandName}`);
        return await interaction.reply({
            content: '❌ Comando não encontrado.',
            ephemeral: true
        });
    }

    try {
        // Log do uso do comando
        logger.info(`Comando executado: /${interaction.commandName} por ${interaction.user.tag} em ${interaction.guild?.name || 'DM'}`);

        // Executa o comando
        await command.execute(interaction);

    } catch (error) {
        logger.error(`Erro ao executar comando ${interaction.commandName}:`, error);
        
        const errorMessage = {
            content: '❌ Houve um erro ao executar este comando.',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}
