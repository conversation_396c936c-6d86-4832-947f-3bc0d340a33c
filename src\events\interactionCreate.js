import { Events } from 'discord.js';
import { logger } from '../utils/logger.js';
import { handleSlashCommand } from '../handlers/slashCommandHandler.js';
import { handleButton } from '../handlers/buttonHandler.js';
import { handleSelectMenu } from '../handlers/selectMenuHandler.js';
import { handleModal } from '../handlers/modalHandler.js';

export default {
    name: Events.InteractionCreate,
    async execute(interaction) {
        try {
            // Log da interação para debug
            logger.debug(`Interação recebida: ${interaction.type} de ${interaction.user.tag}`);

            // Roteamento baseado no tipo de interação
            if (interaction.isChatInputCommand()) {
                await handleSlashCommand(interaction);
            } else if (interaction.isButton()) {
                await handleButton(interaction);
            } else if (interaction.isStringSelectMenu()) {
                await handleSelectMenu(interaction);
            } else if (interaction.isModalSubmit()) {
                await handleModal(interaction);
            } else {
                logger.warn(`Tipo de interação não suportado: ${interaction.type}`);
            }

        } catch (error) {
            logger.error('❌ Erro ao processar interação:', error);
            
            // Tenta responder com erro se a interação ainda não foi respondida
            try {
                const errorMessage = {
                    content: '❌ Ocorreu um erro ao processar sua solicitação. Tente novamente mais tarde.',
                    ephemeral: true
                };

                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(errorMessage);
                } else {
                    await interaction.reply(errorMessage);
                }
            } catch (replyError) {
                logger.error('❌ Erro ao enviar mensagem de erro:', replyError);
            }
        }
    }
};
